globalThis.process??={},globalThis.process.env??={};import{a as createComponent,r as renderComponent,e as renderScript,d as renderTemplate,m as maybeRenderHead}from"../chunks/astro/server_BgKLHZ62.mjs";import{$ as $$Layout}from"../chunks/Layout_b-gpcG6t.mjs";export{renderers}from"../renderers.mjs";const $$CustomerPortal=createComponent((async(e,t,r)=>renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:"Customer Portal - InfPik",description:"Access your orders, downloads, and account information",noindex:!0},{default:async e=>renderTemplate` ${maybeRenderHead()}<section class="py-8 min-h-[60vh]"> <div class="container max-w-4xl"> <!-- Header --> <div class="text-center mb-8"> <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path> </svg> </div> <h1 class="text-4xl font-bold text-primary-900 mb-4">Customer Portal</h1> <p class="text-xl text-primary-600 leading-relaxed">
Access your purchase history, downloads, and manage your account
</p> </div> <!-- Email Form --> <div id="emailForm" class="bg-white rounded-2xl shadow-xl border border-primary-100 p-8 mb-8"> <div class="text-center mb-6"> <h2 class="text-2xl font-semibold text-primary-900 mb-2">Enter Your Email</h2> <p class="text-primary-600">We'll find your orders and account information</p> </div> <form id="customerPortalForm" class="max-w-md mx-auto"> <div class="mb-4"> <label for="customerEmail" class="block text-sm font-medium text-primary-700 mb-2">
Email Address
</label> <input type="email" id="customerEmail" name="email" required class="w-full px-4 py-3 border border-primary-200 rounded-xl bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="<EMAIL>"> </div> <button type="submit" class="btn-primary w-full" id="submitButton"> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640" class="w-5 h-5"><!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path fill="#ffffff" d="M310.6 194.3L243.4 222.5L243.4 107.2L188.7 297.5L243.4 273.3L243.4 403.6L310.6 194.3zM227.4 97.6L226.1 102.3L210.9 155.2C170.6 170.7 142 209.8 142 255.5C142 307.8 176.3 351.4 225.4 361L225.4 414.6C147.5 404.1 90 336.4 90 255.6C90 175.1 149.8 108.4 227.4 97.6zM538.8 544.8C527.6 556 515.7 557.1 510.2 555.3C504.8 553.5 483.1 535.4 449.8 510.9C416.5 486.3 416.2 475.2 406.8 454.2C397.4 433.3 376.4 411.6 349.3 401.8L339.6 387.1C314.9 404 286.6 414 258.3 415.8L260.4 409.2L276.3 359.7C322.8 347.8 357.2 305.7 357.2 255.5C357.2 201 318.8 153.4 261.2 148.4L261.2 96.3C344.4 101.4 410 170.8 410 255.6C410 289.2 398.8 320.3 381 346L395.6 355.6C405.4 382.7 427.1 403.6 448 413C468.9 422.4 480.2 422.7 504.8 456C529.4 489.2 547.5 510.9 549.3 516.3C551.1 521.7 550 533.6 538.8 544.8zM528.9 526.9C528.9 522.5 525.3 518.9 520.9 518.9C516.5 518.9 512.9 522.5 512.9 526.9C512.9 531.3 516.5 534.9 520.9 534.9C525.3 534.9 528.9 531.3 528.9 526.9z"></path></svg>
Find My Orders
</button> </form> <!-- Loading State --> <div id="loadingState" class="hidden text-center py-8"> <div class="flex flex-col items-center justify-center gap-4 mb-4"> <img src="/logo.svg" alt="InfPik" class="w-12 h-12 animate-pulse"> <span class="text-primary-600 font-medium">Searching for your orders...</span> </div> </div> <!-- Error State --> <div id="errorState" class="hidden bg-red-50 border border-red-200 rounded-xl p-4 mt-4"> <div class="flex items-start gap-3"> <svg class="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path> </svg> <div class="flex-1"> <p class="text-red-800 font-medium">Unable to find your account</p> <p class="text-red-600 text-sm" id="errorMessage">Please check your email address and try again.</p> <div class="mt-3"> <button id="retryButton" class="text-red-600 hover:text-red-700 text-sm font-medium underline">
Try Again
</button> </div> </div> </div> </div> </div> <!-- Customer Data Display --> <div id="customerData" class="hidden"> <!-- Customer Info --> <div class="bg-white rounded-2xl shadow-xl border border-primary-100 p-8 mb-8"> <div class="flex items-center justify-between mb-6"> <div> <h2 class="text-2xl font-semibold text-primary-900">Account Information</h2> <p class="text-primary-600" id="customerEmail"></p> </div> <button id="backButton" class="btn-secondary"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path> </svg>
Search Again
</button> </div> <div class="grid md:grid-cols-3 gap-6"> <div class="text-center p-4 bg-primary-50 rounded-xl"> <div class="text-2xl font-bold text-accent-600" id="totalOrders">0</div> <div class="text-primary-600 text-sm">Total Orders</div> </div> <div class="text-center p-4 bg-primary-50 rounded-xl"> <div class="text-2xl font-bold text-accent-600" id="totalSpent">$0</div> <div class="text-primary-600 text-sm">Total Spent</div> </div> <div class="text-center p-4 bg-primary-50 rounded-xl"> <div class="text-2xl font-bold text-accent-600" id="memberSince">-</div> <div class="text-primary-600 text-sm">Member Since</div> </div> </div> </div> <!-- Orders List --> <div class="bg-white rounded-2xl shadow-xl border border-primary-100 p-8"> <h3 class="text-xl font-semibold text-primary-900 mb-6">Order History</h3> <div id="ordersList"> <!-- Orders will be populated here --> </div> <!-- Downloadables --> <div class="mt-10"> <h4 class="text-lg font-semibold text-primary-900 mb-4">Your Downloads</h4> <div id="downloadsList" class="space-y-3"></div> <div id="emptyDownloads" class="hidden text-primary-600 text-sm">No downloadable files yet.</div> </div> <!-- Empty State --> <div id="emptyOrders" class="hidden text-center py-12"> <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4"> <svg class="w-8 h-8 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path> </svg> </div> <h4 class="text-lg font-medium text-primary-900 mb-2">No Orders Found</h4> <p class="text-primary-600 mb-6">You haven't made any purchases yet. Start exploring our collection!</p> <div class="flex flex-col sm:flex-row gap-3 justify-center"> <a href="/products" class="btn-primary"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg>
Browse Our Collection
</a> <a href="/trending" class="btn-secondary"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path> </svg>
View Trending
</a> </div> </div> </div> </div> </div> </section> `})} ${renderScript(e,"D:/code/image/polar-image-store/src/pages/customer-portal.astro?astro&type=script&index=0&lang.ts")}`),"D:/code/image/polar-image-store/src/pages/customer-portal.astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/customer-portal.astro",$$url="/customer-portal",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$CustomerPortal,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};