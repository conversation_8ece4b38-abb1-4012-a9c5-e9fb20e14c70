var b=Object.defineProperty,g=Object.defineProperties,w=Object.getOwnPropertyDescriptors,m=Object.getOwnPropertySymbols,E=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable,f=(d,t,e)=>t in d?b(d,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):d[t]=e,u=(d,t,e)=>new Promise((s,o)=>{var a=n=>{try{i(e.next(n))}catch(c){o(c)}},r=n=>{try{i(e.throw(n))}catch(c){o(c)}},i=n=>n.done?s(n.value):Promise.resolve(n.value).then(a,r);i((e=e.apply(d,t)).next())}),y="POLAR_CHECKOUT",h=class l{constructor(t,e){this.iframe=t,this.loader=e,this.loaded=!1,this.closable=!0,this.eventTarget=new EventTarget,this.initWindowListener(),this.addEventListener("loaded",this.loadedListener.bind(this)),this.addEventListener("close",this.closeListener.bind(this)),this.addEventListener("confirmed",this.confirmedListener.bind(this)),this.addEventListener("success",this.successListener.bind(this))}static postMessage(t,e){var s;window.parent.postMessage((s=((o,a)=>{for(var r in a||(a={}))E.call(a,r)&&f(o,r,a[r]);if(m)for(var r of m(a))L.call(a,r)&&f(o,r,a[r]);return o})({},t),g(s,w({type:y}))),e)}static create(t,e){return u(this,null,function*(){const s=document.createElement("style");s.innerText=`
      .polar-loader-spinner {
        width: 20px;
        aspect-ratio: 1;
        border-radius: 50%;
        background: ${e==="dark"?"#000":"#fff"};
        box-shadow: 0 0 0 0 ${e==="dark"?"#fff":"#000"};
        animation: polar-loader-spinner-animation 1s infinite;
      }
      @keyframes polar-loader-spinner-animation {
        100% {box-shadow: 0 0 0 30px #0000}
      }
      body.polar-no-scroll {
        overflow: hidden;
      }
    `,document.head.appendChild(s);const o=document.createElement("div");o.style.position="absolute",o.style.top="50%",o.style.left="50%",o.style.transform="translate(-50%, -50%)",o.style.zIndex="2147483647",o.style.colorScheme="auto";const a=document.createElement("div");a.className="polar-loader-spinner",o.appendChild(a),document.body.classList.add("polar-no-scroll"),document.body.appendChild(o);const r=new URL(t);r.searchParams.set("embed","true"),r.searchParams.set("embed_origin",window.location.origin),e&&r.searchParams.set("theme",e);const i=r.toString(),n=document.createElement("iframe");n.src=i,n.style.position="fixed",n.style.top="0",n.style.left="0",n.style.width="100%",n.style.height="100%",n.style.border="none",n.style.zIndex="2147483647",n.style.backgroundColor="rgba(0, 0, 0, 0.5)",n.style.colorScheme="auto";const c="https://polar.sh,https://sandbox.polar.sh".split(",").join(" ");n.allow=`payment 'self' ${c}; publickey-credentials-get 'self' ${c};`,document.body.appendChild(n);const p=new l(n,o);return new Promise(v=>{p.addEventListener("loaded",()=>v(p),{once:!0})})})}static init(){document.querySelectorAll("[data-polar-checkout]").forEach(t=>{t.removeEventListener("click",l.checkoutElementClickHandler),t.addEventListener("click",l.checkoutElementClickHandler)})}close(){document.body.removeChild(this.iframe),document.body.classList.remove("polar-no-scroll")}addEventListener(t,e,s){this.eventTarget.addEventListener(t,e,s)}removeEventListener(t,e){this.eventTarget.removeEventListener(t,e)}static checkoutElementClickHandler(t){return u(this,null,function*(){t.preventDefault();let e=t.target;for(;!e.hasAttribute("data-polar-checkout");){if(!e.parentElement)return;e=e.parentElement}const s=e.getAttribute("href")||e.getAttribute("data-polar-checkout"),o=e.getAttribute("data-polar-checkout-theme");l.create(s,o)})}loadedListener(t){t.defaultPrevented||this.loaded||(document.body.removeChild(this.loader),this.loaded=!0)}closeListener(t){t.defaultPrevented||this.closable&&this.close()}confirmedListener(t){t.defaultPrevented||(this.closable=!1)}successListener(t){t.defaultPrevented||(this.closable=!0,t.detail.redirect&&(window.location.href=t.detail.successURL))}initWindowListener(){window.addEventListener("message",({data:t,origin:e})=>{"https://polar.sh,https://sandbox.polar.sh".split(",").includes(e)&&t.type===y&&this.eventTarget.dispatchEvent(new CustomEvent(t.event,{detail:t,cancelable:!0}))})}};if(typeof window<"u"&&(window.Polar={EmbedCheckout:h}),typeof document<"u"){const d=document.currentScript;d&&d.hasAttribute("data-auto-init")&&document.addEventListener("DOMContentLoaded",()=>u(void 0,null,function*(){h.init()}))}class k{constructor(){this.init()}async init(){document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>this.setupCheckout()):this.setupCheckout()}async setupCheckout(){try{h.init(),document.querySelectorAll("[data-polar-checkout]").forEach(e=>{e.addEventListener("click",async s=>{s.preventDefault();const o=e.getAttribute("data-product-id"),a=e.getAttribute("data-polar-checkout-theme")||"light",r=e.id;if(!o){console.error("Product ID not found for checkout trigger");return}try{this.showLoading(r);const i=await this.createCheckoutUrl(o);if(!i)throw new Error("Failed to create checkout URL");const n=await h.create(i,a);this.hideLoading(r),this.setupCheckoutEvents(n,r)}catch(i){console.error("Checkout error:",i),this.hideLoading(r),this.showError("Failed to open checkout. Please try again.")}})})}catch(t){console.error("Failed to initialize embed checkout:",t)}}async createCheckoutUrl(t){try{const e=await fetch("/api/checkout-url",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:t})});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);return(await e.json()).checkoutUrl}catch(e){return console.error("Failed to create checkout URL:",e),null}}setupCheckoutEvents(t,e){const s=()=>{e&&this.hideLoading(e);try{document.querySelectorAll("[data-polar-embed-overlay], .polar-embed-overlay, .polar-embed-loader").forEach(a=>a instanceof HTMLElement&&(a.style.display="none"))}catch{}};t.addEventListener("success",o=>{console.log("Checkout successful:",o.detail),s(),typeof gtag<"u"&&gtag("event","purchase",{event_category:"ecommerce",event_label:"embed_checkout"}),o.detail.checkoutId?window.location.href=`/success?checkout_id=${o.detail.checkoutId}`:window.location.href="/success"}),t.addEventListener("close",o=>{console.log("Checkout closed"),s()}),t.addEventListener("loaded",o=>{console.log("Checkout loaded"),s()}),t.addEventListener("confirmed",o=>{console.log("Payment confirmed, processing...")})}showLoading(t){const e=document.getElementById(`${t}-loading`);e&&e.classList.remove("hidden")}hideLoading(t){const e=document.getElementById(`${t}-loading`);e&&e.classList.add("hidden")}showError(t){const e=document.createElement("div");e.className="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50",e.textContent=t,document.body.appendChild(e),setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},5e3)}}new k;
